# Stack Trace Decoding Guide

This guide explains how to decode production stack traces like `react-dom-client.production.js:674:21` back to actual source file paths like `src/pages/HomePage.tsx`.

## Quick Solutions

### Method 1: Browser Developer Tools (Easiest)

1. **Enable source maps in production** (if not already enabled):
   - Ensure your webpack config has `devtool: 'source-map'` for production builds
   - Make sure source map files are accessible to the browser

2. **Use browser's built-in source map support**:
   - Open Chrome/Firefox Developer Tools
   - Go to Sources tab
   - The browser automatically maps minified code to original sources
   - Click on the stack trace line to jump to original source

### Method 2: Browser Console Script

1. Copy the content of `scripts/browser-source-map-decoder.js` into your browser console
2. Use the provided functions:

```javascript
// Decode a single stack trace line
await decodeStackTrace('react-dom-client.production.js:674:21')

// Decode specific line and column
await quickDecode(674, 21)

// Decode full stack trace
await decodeStackTrace(`
  at Object.exports.unstable_runWithPriority (react-dom-client.production.js:674:21)
  at Object.exports.unstable_runWithPriority (react-dom-client.production.js:123:45)
`)
```

### Method 3: Node.js Script

Use the provided `scripts/decode-stack-trace.js`:

```bash
# Decode from stdin
node scripts/decode-stack-trace.js dist/merged-source-map.js.map
# Then paste your stack trace and press Ctrl+D

# Decode specific line/column
node scripts/decode-stack-trace.js dist/merged-source-map.js.map 674 21

# Decode from file
node scripts/decode-stack-trace.js dist/merged-source-map.js.map error.log
```

## Understanding Source Maps

### What are Source Maps?

Source maps are files that map minified/compiled code back to original source code. They contain:
- Original file names and paths
- Line and column mappings
- Variable and function names
- Source content (optional)

### Common Source Map Locations

In your Expensify App, source maps are typically found at:
- `dist/merged-source-map.js.map` (combined source map)
- `dist/*.js.map` (individual bundle source maps)
- Served alongside JavaScript bundles in production

### Source Map Types in Webpack

Your webpack config uses different source map types:
- **Development**: `eval-source-map` (fast rebuilds, good debugging)
- **Production**: `source-map` (separate files, slower builds, best quality)

## Advanced Usage

### Using Metro Symbolicate

If you're working with React Native or Metro bundler:

```bash
# Install metro-symbolicate globally
npm install -g metro-symbolicate

# Symbolicate stack trace
metro-symbolicate dist/main.jsbundle.map < stacktrace.txt
```

### Using Existing Scripts

Your codebase has existing symbolication utilities:

1. **Profile Symbolication**: `scripts/symbolicate-profile.ts`
2. **Source Map Combination**: `scripts/combine-web-sourcemaps.ts`
3. **DevTools Integration**: `packages/devtools/src/parseErrorStack.tsx`

### Custom Implementation

For custom needs, use the source-map library directly:

```javascript
const { SourceMapConsumer } = require('source-map');
const fs = require('fs');

async function decodePosition(sourceMapFile, line, column) {
    const sourceMapContent = fs.readFileSync(sourceMapFile, 'utf8');
    const consumer = await new SourceMapConsumer(sourceMapContent);
    
    const original = consumer.originalPositionFor({
        line: parseInt(line),
        column: parseInt(column)
    });
    
    consumer.destroy();
    return original;
}
```

## Troubleshooting

### Source Map Not Found

1. **Check if source maps are generated**:
   ```bash
   # Look for .map files in your build output
   find dist -name "*.map"
   ```

2. **Verify webpack configuration**:
   - Ensure `devtool: 'source-map'` is set for production
   - Check that source maps are not excluded from build

3. **Check source map URL in JavaScript files**:
   ```bash
   # Look for sourceMappingURL comments
   tail -n 5 dist/main.js
   ```

### Incorrect Mappings

1. **Ensure source map corresponds to the exact build**:
   - Source maps must match the exact JavaScript bundle
   - Different builds produce different mappings

2. **Check for source map combination issues**:
   - If using multiple bundles, ensure source maps are properly merged
   - Use `scripts/combine-web-sourcemaps.ts` if needed

### Performance Issues

1. **Source maps are large**: This is normal for production builds
2. **Slow decoding**: Consider using the browser's built-in support instead of custom scripts
3. **Memory usage**: Destroy SourceMapConsumer instances after use

## Production Considerations

### Security

- **Don't expose source maps in production** unless necessary for debugging
- Consider serving source maps only to authenticated developers
- Use separate deployment for source maps

### Performance

- Source maps don't affect runtime performance (only loaded when DevTools are open)
- Large source maps can slow down DevTools loading
- Consider using `hidden-source-map` for production if you don't want to expose them

### Deployment

1. **Upload source maps to error tracking services** (Sentry, Bugsnag, etc.)
2. **Store source maps separately** from main application bundles
3. **Version source maps** alongside application releases

## Integration with Error Tracking

### Sentry Integration

```javascript
// Upload source maps to Sentry
const SentryWebpackPlugin = require('@sentry/webpack-plugin');

module.exports = {
  plugins: [
    new SentryWebpackPlugin({
      include: './dist',
      ignore: ['node_modules', 'webpack.config.js'],
    }),
  ],
};
```

### Custom Error Handling

```javascript
// Automatically decode stack traces in error handlers
window.addEventListener('error', async (event) => {
    const stackTrace = event.error.stack;
    const decodedStack = await decodeStackTrace(stackTrace);
    console.log('Decoded stack trace:', decodedStack);
});
```

## Examples

### Common Stack Trace Formats

```
// Chrome format
at Object.exports.unstable_runWithPriority (react-dom-client.production.js:674:21)

// Firefox format  
<EMAIL>:674:21

// Simple format
react-dom-client.production.js:674:21

// With function context
HomePage.render (main.bundle.js:1234:56)
```

### Expected Output

```
Input:  react-dom-client.production.js:674:21
Output: src/components/HomePage/HomePage.tsx:45:12 (HomePage.render)

Input:  main.bundle.js:1234:56  
Output: src/utils/ApiUtils.ts:123:8 (makeApiCall)
```
