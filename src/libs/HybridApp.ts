import HybridAppModule from '@expensify/react-native-hybrid-app';
import Onyx from 'react-native-onyx';
import type {OnyxEntry} from 'react-native-onyx';
import CONFIG from '@src/CONFIG';
import CONST from '@src/CONST';
import ONYXKEYS from '@src/ONYXKEYS';
import type {Credentials, HybridApp, Session, TryNewDot} from '@src/types/onyx';
import {setNewDotSignInState, setReadyToShowAuthScreens, setUseNewDotSignInPage} from './actions/HybridApp';
import {closeReactNativeApp} from './actions/Session';
import Log from './Log';
import {getCurrentUserEmail} from './Network/NetworkStore';

let currentHybridApp: OnyxEntry<HybridApp>;
let currentTryNewDot: OnyxEntry<TryNewDot>;
let currentCredentials: OnyxEntry<Credentials>;

Onyx.connect({
    key: ONYXKEYS.HYBRID_APP,
    callback: (hybridApp) => {
        handleChangeInHybridAppSignInFlow(hybridApp, currentTryNewDot, currentCredentials);
    },
});

Onyx.connect({
    key: ONYXKEYS.NVP_TRY_NEW_DOT,
    callback: (tryNewDot) => {
        handleChangeInHybridAppSignInFlow(currentHybridApp, tryNewDot, currentCredentials);
    },
});

Onyx.connect({
    key: ONYXKEYS.CREDENTIALS,
    callback: (credentials) => {
        currentCredentials = credentials;
        handleChangeInHybridAppSignInFlow(currentHybridApp, currentTryNewDot, credentials);
    },
});

let currentSession: OnyxEntry<Session>;
Onyx.connect({
    key: ONYXKEYS.SESSION,
    callback: (session: OnyxEntry<Session>) => {
        if (!currentSession?.authToken && session?.authToken && currentHybridApp?.newDotSignInState === CONST.HYBRID_APP_SIGN_IN_STATE.STARTED) {
            setNewDotSignInState(CONST.HYBRID_APP_SIGN_IN_STATE.FINISHED);
        }
        currentSession = session;
    },
});

let activePolicyID: OnyxEntry<string>;
Onyx.connect({
    key: ONYXKEYS.NVP_ACTIVE_POLICY_ID,
    callback: (newActivePolicyID) => {
        activePolicyID = newActivePolicyID;
    },
});

function shouldUseOldApp(tryNewDot?: TryNewDot) {
    if (!!tryNewDot && !tryNewDot.classicRedirect) {
        return true;
    }
    return tryNewDot?.classicRedirect?.dismissed === true;
}

function handleChangeInHybridAppSignInFlow(hybridApp: OnyxEntry<HybridApp>, tryNewDot: OnyxEntry<TryNewDot>, credentials: OnyxEntry<Credentials>) {
    if (!CONFIG.IS_HYBRID_APP) {
        return;
    }

    if (!hybridApp?.useNewDotSignInPage) {
        currentHybridApp = hybridApp;
        currentTryNewDot = tryNewDot;
        return;
    }

    if (hybridApp?.newDotSignInState === CONST.HYBRID_APP_SIGN_IN_STATE.FINISHED && tryNewDot !== undefined && !!credentials?.autoGeneratedLogin && !!credentials?.autoGeneratedPassword) {
        // It's better to not pass function directly to Log.info to avoid bugs with evaluation
        const shouldUseOD = shouldUseOldApp(tryNewDot);
        Log.info(`[HybridApp] Performing sign-in${shouldUseOD ? '' : ' (in background)'} on OldDot side`);
        HybridAppModule.signInToOldDot({
            autoGeneratedLogin: credentials.autoGeneratedLogin,
            autoGeneratedPassword: credentials.autoGeneratedPassword,
            authToken: currentSession?.authToken ?? '',
            email: getCurrentUserEmail() ?? '',
            // eslint-disable-next-line rulesdir/no-default-id-values
            policyID: activePolicyID ?? '',
        });
        setUseNewDotSignInPage(false).then(() => {
            if (shouldUseOD) {
                closeReactNativeApp({shouldSignOut: false, shouldSetNVP: false});
            } else {
                Log.info('[HybridApp] The user should see NewDot. There is no need to block the user on the `SignInPage` until the sign-in process is completed on the OldDot side.');
                setReadyToShowAuthScreens(true);
            }
        });
    }

    currentHybridApp = hybridApp;
    currentTryNewDot = tryNewDot;
}
