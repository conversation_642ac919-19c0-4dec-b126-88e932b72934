# Expensify-Common Text Parsing Analysis

## Overview

Expensify-common is a shared library that provides text parsing functionality, primarily through the `ExpensiMark` class. This class converts markdown-like text to HTML and vice versa, with special handling for various formatting elements including blockquotes, newlines, and other markdown elements.

## Core Architecture

### Main Components

1. **ExpensiMark Class**: The main parser class that handles text-to-HTML conversion
2. **Rules System**: An array of rules that define how different markdown elements are processed
3. **Processing Pipeline**: Sequential application of rules to transform text

### Key Files
- `ExpensiMark.js/ts`: Main parser implementation
- `Parser.ts`: App-specific wrapper that extends ExpensiMark with context

## How Text Processing Works

### 1. Input Processing Flow

When text is passed to ExpensiMark, it follows this flow:

```
Input Text → Escape HTML (optional) → Apply Rules Sequentially → Output HTML
```

### 2. Rules Processing Order

The rules are applied in a specific order (important for correct parsing):

1. **emoji** - Process emoji first to avoid formatting conflicts
2. **codeFence** - Handle code blocks (```code```)
3. **video** - Process video markdown
4. **inlineCodeBlock** - Handle inline code (`code`)
5. **email** - Process email links
6. **heading1** - Handle # headings
7. **image** - Process image markdown
8. **link** - Handle markdown links
9. **mentions** - Process @mentions and #room-mentions
10. **autolink** - Auto-link URLs
11. **quote** - **Process blockquotes** ⭐
12. **italic** - Handle _italic_ text
13. **autoEmail** - Auto-link email addresses
14. **shortMentions** - Handle short mentions
15. **bold** - Handle *bold* text
16. **strikethrough** - Handle ~strikethrough~ text
17. **newline** - **Convert newlines to `<br />`** ⭐
18. **replacepre** - Clean up `<br />` after `</pre>`
19. **replaceh1br** - Clean up `<br />` after `</h1>`

## Blockquote Parsing Deep Dive

### 1. Quote Rule Implementation

The blockquote parsing is handled by the `quote` rule:

```javascript
{
    name: 'quote',
    process: (textToProcess, replacement, shouldKeepRawInput = false) => {
        const regex = /^(?:&gt;)+ +(?! )(?![^<]*(?:<\/pre>|<\/code>|<\/video>))([^\v\n\r]*)/gm;
        let replacedText = this.replaceTextWithExtras(textToProcess, regex, EXTRAS_DEFAULT, replacement);
        
        if (shouldKeepRawInput) {
            return replacedText;
        }
        
        // Handle nested quotes by removing redundant blockquote tags
        for (let i = this.maxQuoteDepth; i > 0; i--) {
            replacedText = replacedText.replaceAll(
                `${'</blockquote>'.repeat(i)}\n${'<blockquote>'.repeat(i)}`, 
                '\n'
            );
        }
        replacedText = replacedText.replaceAll('</blockquote>\n', '</blockquote>');
        return replacedText;
    },
    replacement: (_extras, g1) => {
        const { replacedText } = this.replaceQuoteText(g1, false);
        return `<blockquote>${replacedText || ' '}</blockquote>`;
    }
}
```

### 2. Quote Processing Logic

#### Regex Pattern Breakdown
```javascript
/^(?:&gt;)+ +(?! )(?![^<]*(?:<\/pre>|<\/code>|<\/video>))([^\v\n\r]*)/gm
```

- `^(?:&gt;)+` - Matches one or more `&gt;` (escaped `>`) at line start
- ` +(?! )` - Matches one or more spaces, but not if followed by another space
- `(?![^<]*(?:<\/pre>|<\/code>|<\/video>))` - Negative lookahead to avoid processing quotes inside code/video blocks
- `([^\v\n\r]*)` - Captures the quote content (everything except vertical whitespace)
- `gm` flags - Global and multiline matching

#### Quote Text Processing

The `replaceQuoteText` method handles the core quote logic:

```javascript
replaceQuoteText(text, shouldKeepRawInput) {
    let isStartingWithSpace = false;
    const handleMatch = (_match, g2) => {
        isStartingWithSpace = !!g2;
        return '';
    };
    
    // Remove the > prefix and detect if there was a space
    const textToReplace = text.replace(/^&gt;( )?/gm, handleMatch);
    
    const filterRules = ['heading1'];
    
    // Allow recursive quote processing up to maxQuoteDepth
    if (this.currentQuoteDepth < this.maxQuoteDepth - 1 && !isStartingWithSpace) {
        filterRules.push('quote');
        this.currentQuoteDepth++;
    }
    
    // Recursively process the quote content
    const replacedText = this.replace(textToReplace, {
        filterRules,
        shouldEscapeText: false,
        shouldKeepRawInput,
    });
    
    this.currentQuoteDepth = 0;
    return { replacedText, shouldAddSpace: isStartingWithSpace };
}
```

### 3. Nested Quote Handling

ExpensiMark supports nested quotes up to a maximum depth (default: 3 levels):

```
> Level 1 quote
> > Level 2 quote  
> > > Level 3 quote
> Back to level 1
```

The nested quote processing:
1. Detects quote depth by counting `>` symbols
2. Recursively processes inner quotes
3. Cleans up redundant `<blockquote>` tags
4. Maintains proper nesting structure

### 4. Quote Output Examples

**Input:**
```
> This is a quote
> > This is a nested quote
> Back to first level
```

**Output:**
```html
<blockquote>This is a quote</blockquote>
<blockquote><blockquote>This is a nested quote</blockquote></blockquote>
<blockquote>Back to first level</blockquote>
```

## Newline and `<br />` Handling

### 1. Newline Processing Rule

The `newline` rule converts line breaks to HTML:

```javascript
{
    name: 'newline',
    regex: /\r?\n/g,
    replacement: '<br />',
}
```

### 2. Cleanup Rules

After newline conversion, cleanup rules remove redundant `<br />` tags:

#### replacepre Rule
```javascript
{
    name: 'replacepre',
    regex: /<\/pre>\s*<br\s*[/]?>/gi,
    replacement: '</pre>',
}
```

#### replaceh1br Rule
```javascript
{
    name: 'replaceh1br',
    regex: /<\/h1><br\s*[/]?>/gi,
    replacement: '</h1>',
}
```

### 3. Whitespace Rules Control

The parser has a `shouldKeepWhitespace` option that affects newline handling:

```javascript
whitespaceRulesToDisable = ['newline', 'replacepre', 'replacebr', 'replaceh1br'];

shouldKeepWhitespaceRules = this.rules.filter(rule => 
    !this.whitespaceRulesToDisable.includes(rule.name)
);
```

When `shouldKeepRawInput` is true, whitespace-related rules are disabled.

## Newlines Inside Blockquotes

### 1. How It Works

Newlines inside blockquotes are handled through the sequential rule processing:

1. **Quote rule processes first**: Converts `> text` to `<blockquote>text</blockquote>`
2. **Newline rule processes after**: Converts `\n` to `<br />` within the blockquote content
3. **Cleanup rules**: Remove unnecessary `<br />` tags

### 2. Example Processing

**Input:**
```
> First line of quote
> Second line of quote
> 
> Third line after empty line
```

**Step-by-step processing:**

1. **After quote rule:**
```html
<blockquote>First line of quote</blockquote>
<blockquote>Second line of quote</blockquote>
<blockquote> </blockquote>
<blockquote>Third line after empty line</blockquote>
```

2. **After newline rule:**
```html
<blockquote>First line of quote</blockquote><br />
<blockquote>Second line of quote</blockquote><br />
<blockquote> </blockquote><br />
<blockquote>Third line after empty line</blockquote>
```

3. **After cleanup (nested quote consolidation):**
```html
<blockquote>First line of quote<br />Second line of quote<br /> <br />Third line after empty line</blockquote>
```

### 3. Special Cases

#### Empty Quote Lines
```
> First line
> 
> Third line
```
Results in: `<blockquote>First line<br /> <br />Third line</blockquote>`

#### Mixed Content in Quotes
```
> This is **bold** text
> This is _italic_ text
```

The quote content is recursively processed, so formatting within quotes works:
```html
<blockquote>This is <strong>bold</strong> text<br />This is <em>italic</em> text</blockquote>
```

## HTML to Markdown Conversion

### 1. Reverse Processing

ExpensiMark also handles HTML-to-Markdown conversion with specific rules for quotes:

```javascript
{
    name: 'quote',
    regex: /<(blockquote|q)(?:"[^"]*"|'[^']*'|[^'">])*>([\s\S]*?)<\/\1>(?![^<]*(<\/pre>|<\/code>))/gi,
    replacement: (_extras, _match, _g1, g2) => {
        // Process nested blockquotes and convert to markdown
        let resultString = g2
            .replace(/\n?(<h1># )/g, '$1')
            .replace(/(<h1>|<\/h1>)+/g, '\n')
            .trim()
            .split('\n');
        
        // Wrap each line with blockquote tags
        resultString = resultString.map((line) => {
            return `<blockquote>${line}</blockquote>`;
        });
        
        // Convert to markdown format with > prefix
        resultString = resultString
            .map((text) => {
                let modifiedText = text;
                let depth;
                do {
                    depth = (modifiedText.match(/<blockquote>/gi) || []).length;
                    modifiedText = modifiedText.replace(/<blockquote>/gi, '');
                    modifiedText = modifiedText.replace(/<\/blockquote>/gi, '');
                } while (/<blockquote>/i.test(modifiedText));
                
                return `${'>'.repeat(depth)} ${modifiedText}`;
            })
            .join('\n');
        
        return `<blockquote>${resultString}</blockquote>`;
    },
}
```

## Key Features and Limitations

### ✅ Supported Features

1. **Nested blockquotes** up to 3 levels deep
2. **Newlines within blockquotes** are preserved as `<br />` tags
3. **Mixed formatting** within quotes (bold, italic, links, etc.)
4. **Empty quote lines** are handled with space placeholders
5. **Bidirectional conversion** (Markdown ↔ HTML)
6. **Code block protection** - quotes inside code blocks are ignored

### ⚠️ Limitations

1. **Maximum quote depth**: Limited to 3 levels (`maxQuoteDepth = 3`)
2. **Space sensitivity**: Requires space after `>` symbol
3. **Line-by-line processing**: Each line with `>` becomes a separate blockquote initially
4. **Regex complexity**: Complex patterns may have edge cases

### 🔧 Configuration Options

- `shouldKeepRawInput`: Preserves original formatting
- `shouldEscapeText`: Controls HTML escaping
- `filterRules`: Apply only specific rules
- `disabledRules`: Skip specific rules
- `maxQuoteDepth`: Control nesting depth

## Usage Examples

### Basic Usage
```javascript
import Parser from '@libs/Parser';

const markdown = `
> This is a quote
> > Nested quote
> Back to first level
`;

const html = Parser.replace(markdown);
// Result: <blockquote>This is a quote<br /><blockquote>Nested quote</blockquote><br />Back to first level</blockquote>
```

### With Options
```javascript
const html = Parser.replace(markdown, {
    shouldKeepRawInput: true,
    filterRules: ['quote', 'bold', 'italic']
});
```

### HTML to Markdown
```javascript
const html = '<blockquote>This is a quote</blockquote>';
const markdown = Parser.htmlToMarkdown(html);
// Result: "> This is a quote"
```

## Conclusion

Expensify-common's text parsing system is a sophisticated markdown processor that handles blockquotes and newlines through a rule-based system. The sequential processing ensures proper handling of nested elements while maintaining compatibility with various formatting options. The system successfully handles newlines within blockquotes by converting them to `<br />` tags and provides robust support for nested quotes up to 3 levels deep.